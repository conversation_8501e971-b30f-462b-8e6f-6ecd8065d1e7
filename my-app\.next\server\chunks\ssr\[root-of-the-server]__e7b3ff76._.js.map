{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Portfolio/my-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function Portfolio() {\n  const [isVisible, setIsVisible] = useState(false);\n  const [activeSection, setActiveSection] = useState('hero');\n\n  useEffect(() => {\n    setIsVisible(true);\n\n    const handleScroll = () => {\n      const sections = ['hero', 'about', 'skills', 'projects', 'contact'];\n      const scrollPosition = window.scrollY + 100;\n\n      for (const section of sections) {\n        const element = document.getElementById(section);\n        if (element) {\n          const { offsetTop, offsetHeight } = element;\n          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n            setActiveSection(section);\n            break;\n          }\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    element?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"text-2xl font-bold text-white\">Portfolio</div>\n            <div className=\"hidden md:flex space-x-8\">\n              {['hero', 'about', 'skills', 'projects', 'contact'].map((section) => (\n                <button\n                  key={section}\n                  onClick={() => scrollToSection(section)}\n                  className={`capitalize transition-colors duration-300 ${\n                    activeSection === section ? 'text-purple-400' : 'text-white hover:text-purple-300'\n                  }`}\n                >\n                  {section === 'hero' ? 'Home' : section}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section id=\"hero\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20\"></div>\n        <div className=\"text-center z-10 px-4\">\n          <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>\n            <h1 className=\"text-5xl md:text-7xl font-bold text-white mb-6\">\n              <span className=\"bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                Your Name\n              </span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto\">\n              Full Stack Developer & UI/UX Designer\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button\n                onClick={() => scrollToSection('projects')}\n                className=\"px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105\"\n              >\n                View My Work\n              </button>\n              <button\n                onClick={() => scrollToSection('contact')}\n                className=\"px-8 py-3 border-2 border-purple-400 text-purple-400 rounded-full hover:bg-purple-400 hover:text-white transition-all duration-300\"\n              >\n                Get In Touch\n              </button>\n            </div>\n          </div>\n        </div>\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n          </svg>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section id=\"about\" className=\"py-20 px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">About Me</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-blue-400 mx-auto\"></div>\n          </div>\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <div className=\"space-y-6\">\n              <p className=\"text-lg text-gray-300 leading-relaxed\">\n                I'm a passionate full-stack developer with over 5 years of experience creating\n                digital experiences that make a difference. I love turning complex problems\n                into simple, beautiful, and intuitive solutions.\n              </p>\n              <p className=\"text-lg text-gray-300 leading-relaxed\">\n                When I'm not coding, you can find me exploring new technologies, contributing\n                to open source projects, or sharing my knowledge through blog posts and tutorials.\n              </p>\n              <div className=\"flex flex-wrap gap-4\">\n                <span className=\"px-4 py-2 bg-purple-600/20 text-purple-300 rounded-full\">React</span>\n                <span className=\"px-4 py-2 bg-blue-600/20 text-blue-300 rounded-full\">TypeScript</span>\n                <span className=\"px-4 py-2 bg-green-600/20 text-green-300 rounded-full\">Node.js</span>\n                <span className=\"px-4 py-2 bg-yellow-600/20 text-yellow-300 rounded-full\">Python</span>\n              </div>\n            </div>\n            <div className=\"relative\">\n              <div className=\"w-80 h-80 mx-auto bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center\">\n                <div className=\"w-72 h-72 bg-slate-800 rounded-full flex items-center justify-center\">\n                  <span className=\"text-6xl\">👨‍💻</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Skills Section */}\n      <section id=\"skills\" className=\"py-20 px-4 bg-black/20\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Skills</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-blue-400 mx-auto\"></div>\n          </div>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {[\n              { category: 'Frontend', skills: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Vue.js'] },\n              { category: 'Backend', skills: ['Node.js', 'Python', 'PostgreSQL', 'MongoDB', 'GraphQL'] },\n              { category: 'Tools', skills: ['Git', 'Docker', 'AWS', 'Figma', 'Jest'] }\n            ].map((skillGroup) => (\n              <div key={skillGroup.category} className=\"bg-white/5 backdrop-blur-sm rounded-xl p-6 hover:bg-white/10 transition-all duration-300\">\n                <h3 className=\"text-2xl font-bold text-white mb-6\">{skillGroup.category}</h3>\n                <div className=\"space-y-4\">\n                  {skillGroup.skills.map((skill, skillIndex) => (\n                    <div key={skill} className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-300\">{skill}</span>\n                      <div className=\"w-32 bg-gray-700 rounded-full h-2\">\n                        <div\n                          className=\"bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-1000\"\n                          style={{ width: `${85 + skillIndex * 3}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Section */}\n      <section id=\"projects\" className=\"py-20 px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Projects</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-blue-400 mx-auto\"></div>\n          </div>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[\n              {\n                title: 'E-Commerce Platform',\n                description: 'A full-stack e-commerce solution built with React, Node.js, and PostgreSQL.',\n                tech: ['React', 'Node.js', 'PostgreSQL'],\n                image: '🛒'\n              },\n              {\n                title: 'Task Management App',\n                description: 'A collaborative task management application with real-time updates.',\n                tech: ['Next.js', 'Socket.io', 'MongoDB'],\n                image: '📋'\n              },\n              {\n                title: 'Weather Dashboard',\n                description: 'A beautiful weather dashboard with interactive charts and forecasts.',\n                tech: ['Vue.js', 'Chart.js', 'API'],\n                image: '🌤️'\n              },\n              {\n                title: 'Social Media App',\n                description: 'A social media platform with real-time messaging and content sharing.',\n                tech: ['React Native', 'Firebase', 'Redux'],\n                image: '📱'\n              },\n              {\n                title: 'Portfolio Website',\n                description: 'A responsive portfolio website with smooth animations and modern design.',\n                tech: ['Next.js', 'Tailwind', 'Framer Motion'],\n                image: '💼'\n              },\n              {\n                title: 'AI Chatbot',\n                description: 'An intelligent chatbot powered by machine learning and natural language processing.',\n                tech: ['Python', 'TensorFlow', 'Flask'],\n                image: '🤖'\n              }\n            ].map((project) => (\n              <div key={project.title} className=\"bg-white/5 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-white/10 transition-all duration-300 transform hover:scale-105\">\n                <div className=\"p-6\">\n                  <div className=\"text-6xl mb-4 text-center\">{project.image}</div>\n                  <h3 className=\"text-xl font-bold text-white mb-3\">{project.title}</h3>\n                  <p className=\"text-gray-300 mb-4\">{project.description}</p>\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {project.tech.map((tech) => (\n                      <span key={tech} className=\"px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm\">\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                  <div className=\"flex gap-4\">\n                    <button className=\"flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300\">\n                      Live Demo\n                    </button>\n                    <button className=\"flex-1 px-4 py-2 border border-purple-400 text-purple-400 rounded-lg hover:bg-purple-400 hover:text-white transition-all duration-300\">\n                      Code\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contact\" className=\"py-20 px-4 bg-black/20\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Get In Touch</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-blue-400 mx-auto\"></div>\n            <p className=\"text-xl text-gray-300 mt-6\">\n              Let's work together to bring your ideas to life!\n            </p>\n          </div>\n          <div className=\"grid md:grid-cols-2 gap-12\">\n            <div className=\"space-y-8\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold text-white\">Email</h3>\n                  <p className=\"text-gray-300\"><EMAIL></p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold text-white\">Phone</h3>\n                  <p className=\"text-gray-300\">+****************</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold text-white\">Location</h3>\n                  <p className=\"text-gray-300\">San Francisco, CA</p>\n                </div>\n              </div>\n            </div>\n            <form className=\"space-y-6\">\n              <div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Your Name\"\n                  className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-colors duration-300\"\n                />\n              </div>\n              <div>\n                <input\n                  type=\"email\"\n                  placeholder=\"Your Email\"\n                  className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-colors duration-300\"\n                />\n              </div>\n              <div>\n                <textarea\n                  rows={5}\n                  placeholder=\"Your Message\"\n                  className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-colors duration-300 resize-none\"\n                ></textarea>\n              </div>\n              <button\n                type=\"submit\"\n                className=\"w-full px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105\"\n              >\n                Send Message\n              </button>\n            </form>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-8 px-4 border-t border-white/10\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 Your Name. All rights reserved.\n          </p>\n          <div className=\"flex justify-center space-x-6 mt-4\">\n            <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300\">\n              GitHub\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300\">\n              LinkedIn\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 transition-colors duration-300\">\n              Twitter\n            </a>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QAEb,MAAM,eAAe;YACnB,MAAM,WAAW;gBAAC;gBAAQ;gBAAS;gBAAU;gBAAY;aAAU;YACnE,MAAM,iBAAiB,OAAO,OAAO,GAAG;YAExC,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;gBACxC,IAAI,SAAS;oBACX,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG;oBACpC,IAAI,kBAAkB,aAAa,iBAAiB,YAAY,cAAc;wBAC5E,iBAAiB;wBACjB;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,SAAS,eAAe;YAAE,UAAU;QAAS;IAC/C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAQ;oCAAS;oCAAU;oCAAY;iCAAU,CAAC,GAAG,CAAC,CAAC,wBACvD,8OAAC;wCAEC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,0CAA0C,EACpD,kBAAkB,UAAU,oBAAoB,oCAChD;kDAED,YAAY,SAAS,SAAS;uCAN1B;;;;;;;;;;;;;;;;;;;;;;;;;;0BAejB,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;;kCAC3B,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,4BAA4B;;8CAC9H,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;8CAI/F,8OAAC;oCAAE,WAAU;8CAA2D;;;;;;8CAGxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAMP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAqB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC5E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;sDAKrD,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;sDAIrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA0D;;;;;;8DAC1E,8OAAC;oDAAK,WAAU;8DAAsD;;;;;;8DACtE,8OAAC;oDAAK,WAAU;8DAAwD;;;;;;8DACxE,8OAAC;oDAAK,WAAU;8DAA0D;;;;;;;;;;;;;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,8OAAC;gBAAQ,IAAG;gBAAS,WAAU;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,UAAU;oCAAY,QAAQ;wCAAC;wCAAS;wCAAW;wCAAc;wCAAgB;qCAAS;gCAAC;gCAC7F;oCAAE,UAAU;oCAAW,QAAQ;wCAAC;wCAAW;wCAAU;wCAAc;wCAAW;qCAAU;gCAAC;gCACzF;oCAAE,UAAU;oCAAS,QAAQ;wCAAC;wCAAO;wCAAU;wCAAO;wCAAS;qCAAO;gCAAC;6BACxE,CAAC,GAAG,CAAC,CAAC,2BACL,8OAAC;oCAA8B,WAAU;;sDACvC,8OAAC;4CAAG,WAAU;sDAAsC,WAAW,QAAQ;;;;;;sDACvE,8OAAC;4CAAI,WAAU;sDACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC7B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,KAAK,aAAa,EAAE,CAAC,CAAC;gEAAC;;;;;;;;;;;;mDALtC;;;;;;;;;;;mCAJN,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;0BAsBrC,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;wCAAC;wCAAS;wCAAW;qCAAa;oCACxC,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;wCAAC;wCAAW;wCAAa;qCAAU;oCACzC,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;wCAAC;wCAAU;wCAAY;qCAAM;oCACnC,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;wCAAC;wCAAgB;wCAAY;qCAAQ;oCAC3C,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;wCAAC;wCAAW;wCAAY;qCAAgB;oCAC9C,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;wCAAC;wCAAU;wCAAc;qCAAQ;oCACvC,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC;oCAAwB,WAAU;8CACjC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B,QAAQ,KAAK;;;;;;0DACzD,8OAAC;gDAAG,WAAU;0DAAqC,QAAQ,KAAK;;;;;;0DAChE,8OAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;wDAAgB,WAAU;kEACxB;uDADQ;;;;;;;;;;0DAKf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAO,WAAU;kEAA0J;;;;;;kEAG5K,8OAAC;wDAAO,WAAU;kEAAwI;;;;;;;;;;;;;;;;;;mCAhBtJ,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BA4B/B,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC5E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAGjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC5E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAGjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;;0EAC5E,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;0EACrE,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAInC,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;;;;;;sDAGd,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;;;;;;sDAGd,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM;gDACN,aAAY;gDACZ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAqE;;;;;;8CAG3F,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAqE;;;;;;8CAG3F,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvG", "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Portfolio/my-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Portfolio/my-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Portfolio/my-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}